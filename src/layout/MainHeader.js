// @mui
import { styled, useTheme } from '@mui/material/styles';
import { App<PERSON><PERSON>, Too<PERSON>bar, Container, Stack, Typography } from '@mui/material';
// hooks
import useOffSetTop from '../hooks/useOffSetTop';
// utils
import cssStyles from '../utils/cssStyles';
// config
import { HEADER } from '../config';
// components
import Logo from '../components/Logo';
//

import SettingPopover from './SettingPopover';
import LanguagePopover from './LanguagePopover';
// hook
import useAuth from '../hooks/useAuth';

// ----------------------------------------------------------------------

const ToolbarStyle = styled(Toolbar)(({ theme }) => ({
  height: HEADER.MOBILE_HEIGHT,
  transition: theme.transitions.create(['height', 'background-color'], {
    easing: theme.transitions.easing.easeInOut,
    duration: theme.transitions.duration.shorter,
  }),
  [theme.breakpoints.up('md')]: {
    height: HEADER.MAIN_DESKTOP_HEIGHT,
  },
}));

// ----------------------------------------------------------------------

export default function MainHeader() {
  const isOffset = useOffSetTop(HEADER.MAIN_DESKTOP_HEIGHT);

  const theme = useTheme();

  const { user } = useAuth();

  return (
    <AppBar sx={{ boxShadow: 0, bgcolor: 'transparent' }}>
      <ToolbarStyle
        disableGutters
        sx={{
          ...(isOffset && {
            ...cssStyles(theme).bgBlur(),
            height: { md: HEADER.MAIN_DESKTOP_HEIGHT - 16 },
          }),
        }}
      >
        <Container>

          <Stack direction="row" justifyContent="space-between" alignItems={"center"} >
            <Logo />
            <Typography>
              {user?.username}{user?.device?.deviceName && ` - ${user?.device?.deviceName}`}
            </Typography>

            <Stack justifyContent={"space-between"} alignItems={"center"} direction="row" gap={1}>
              <LanguagePopover />
              <SettingPopover />
            </Stack>

          </Stack>

        </Container>
      </ToolbarStyle>
    </AppBar>
  );
}
