import { pxToRem, responsiveFontSizes } from '../utils/getFontValue';

// ----------------------------------------------------------------------

const FONT_PRIMARY = 'Public Sans, sans-serif'; // Google Font
// const FONT_SECONDARY = 'CircularStd, sans-serif'; // Local Font

// Car Control System Fonts - Standardized
const FONT_DIGITAL = 'Orbitron, monospace'; // Digital display font (speed, critical status)
const FONT_AUTOMOTIVE = 'Roboto Mono, monospace'; // Primary automotive interface font
const FONT_MONOSPACE = 'Roboto Mono, monospace'; // Unified monospace font

const typography = {
  fontFamily: FONT_PRIMARY,
  fontWeightRegular: 400,
  fontWeightMedium: 600,
  fontWeightBold: 700,
  h1: {
    fontWeight: 700,
    lineHeight: 80 / 64,
    fontSize: pxToRem(40),
    letterSpacing: 2,
    ...responsiveFontSizes({ sm: 52, md: 58, lg: 64 }),
  },
  h2: {
    fontWeight: 700,
    lineHeight: 64 / 48,
    fontSize: pxToRem(32),
    ...responsiveFontSizes({ sm: 40, md: 44, lg: 48 }),
  },
  h3: {
    fontWeight: 700,
    lineHeight: 1.5,
    fontSize: pxToRem(24),
    ...responsiveFontSizes({ sm: 26, md: 30, lg: 32 }),
  },
  h4: {
    fontWeight: 700,
    lineHeight: 1.5,
    fontSize: pxToRem(20),
    ...responsiveFontSizes({ sm: 20, md: 24, lg: 24 }),
  },
  h5: {
    fontWeight: 700,
    lineHeight: 1.5,
    fontSize: pxToRem(18),
    ...responsiveFontSizes({ sm: 19, md: 20, lg: 20 }),
  },
  h6: {
    fontWeight: 700,
    lineHeight: 28 / 18,
    fontSize: pxToRem(17),
    ...responsiveFontSizes({ sm: 18, md: 18, lg: 18 }),
  },
  subtitle1: {
    fontWeight: 600,
    lineHeight: 1.5,
    fontSize: pxToRem(16),
  },
  subtitle2: {
    fontWeight: 600,
    lineHeight: 22 / 14,
    fontSize: pxToRem(14),
  },
  body1: {
    lineHeight: 1.5,
    fontSize: pxToRem(16),
  },
  body2: {
    lineHeight: 22 / 14,
    fontSize: pxToRem(14),
  },
  caption: {
    lineHeight: 1.5,
    fontSize: pxToRem(12),
  },
  overline: {
    fontWeight: 700,
    lineHeight: 1.5,
    fontSize: pxToRem(12),
    textTransform: 'uppercase',
  },
  button: {
    fontWeight: 700,
    lineHeight: 24 / 14,
    fontSize: pxToRem(14),
    textTransform: 'capitalize',
  },
  // Car Control System Typography - Standardized
  digitalDisplay: {
    fontFamily: FONT_DIGITAL, // Reserved for speed and critical status
    fontWeight: 600,
    fontSize: pxToRem(16),
    letterSpacing: '0.1em',
    lineHeight: 1.4,
  },
  automotiveData: {
    fontFamily: FONT_AUTOMOTIVE, // Primary font for all indicators
    fontWeight: 500, // Values
    fontSize: pxToRem(14),
    letterSpacing: '0.05em', // Standardized spacing
    lineHeight: 1.3,
  },
  automotiveLabel: {
    fontFamily: FONT_AUTOMOTIVE, // Primary font for all indicators
    fontWeight: 400, // Labels
    fontSize: pxToRem(14),
    letterSpacing: '0.05em', // Standardized spacing
    lineHeight: 1.3,
  },
  technicalInfo: {
    fontFamily: FONT_AUTOMOTIVE, // Unified with automotive font
    fontWeight: 400,
    fontSize: pxToRem(12),
    letterSpacing: '0.05em', // Standardized spacing
    lineHeight: 1.2,
  },
  carStatus: {
    fontFamily: FONT_DIGITAL, // Reserved for critical status
    fontWeight: 700,
    fontSize: pxToRem(18),
    letterSpacing: '0.15em',
    lineHeight: 1.5,
  },
};

export default typography;
