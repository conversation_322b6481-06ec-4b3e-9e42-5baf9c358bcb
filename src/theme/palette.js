import { alpha } from '@mui/material/styles';

// ----------------------------------------------------------------------

function createGradient(color1, color2) {
  return `linear-gradient(to bottom, ${color1}, ${color2})`;
}

// SETUP COLORS
const PRIMARY = {
  lighter: '#2ee7ff',
  light: '#38e8ff',
  main: '#33848f',
  dark: '#01060a',
  darker: '#00060a'
};
const SECONDARY = {
  lighter: '#D6E4FF',
  light: '#84A9FF',
  main: '#3366FF',
  dark: '#1939B7',
  darker: '#091A7A',
}; 
const INFO = {
  lighter: '#D0F2FF',
  light: '#74CAFF', 
  main: '#1890FF',
  dark: '#0C53B7',
  darker: '#04297A',
};
const SUCCESS = {
  lighter: '#E9FCD4',
  light: '#AAF27F',
  main: '#54D62C',
  dark: '#229A16',
  darker: '#08660D',
};
const WARNING = {
  lighter: '#FFF7CD',
  light: '#FFE16A',
  main: '#FFC107',
  dark: '#B78103',
  darker: '#7A4F01',
};
const ERROR = {
  lighter: '#FFE7D9',
  light: '#FFA48D',
  main: '#FF4842',
  dark: '#B72136',
  darker: '#7A0C2E',
};

const GREY = {
  0: '#FFFFFF',
  100: '#38e8ff',
  200: '#38B1FF',
  300: '#D0F2FF',
  400: '#004F99',
  500: '#38e8ff',
  600: '#061C2A',
  700: '#0a1217',
  800: '#040e16',
  900: '#00060a',
  500_8: alpha('#38e8ff', 0.08),
  500_12: alpha('#38e8ff', 0.12),
  500_16: alpha('#38e8ff', 0.16),
  500_24: alpha('#38e8ff', 0.24),
  500_32: alpha('#38e8ff', 0.32),
  500_48: alpha('#38e8ff', 0.48),
  500_56: alpha('#38e8ff', 0.56),
  500_80: alpha('#38e8ff', 0.8),
};

const GRADIENTS = {
  primary: createGradient(PRIMARY.light, PRIMARY.main),
  info: createGradient(INFO.light, INFO.main),
  success: createGradient(SUCCESS.light, SUCCESS.main),
  warning: createGradient(WARNING.light, WARNING.main),
  error: createGradient(ERROR.light, ERROR.main),
};

const COMMON = {
  common: { black: '#000', white: '#f0f0f0' },
  primary: { ...PRIMARY, contrastText: '#f0f0f0' },
  secondary: { ...SECONDARY, contrastText: '#f0f0f0' },
  info: { ...INFO, contrastText: '#f0f0f0' },
  success: { ...SUCCESS, contrastText: GREY[800] },
  warning: { ...WARNING, contrastText: GREY[800] },
  error: { ...ERROR, contrastText: '#f0f0f0' },
  grey: GREY,
  gradients: GRADIENTS,
  divider: GREY[500_24],
  action: {
    hover: GREY[700],
    selected: GREY[700],
    disabled: GREY[500_80],
    disabledBackground: GREY[500_24],
    focus: GREY[700],
    hoverOpacity: 0.8,
    disabledOpacity: 0.48,
  },
};

const palette = {
  dark: {
    ...COMMON,
    mode: 'dark',
    text: { primary: '#f0f0f0', secondary: '#f0f0f0', disabled: '#a3a3a3' },
    background: {
      paper: '#000000',    // Pure black for premium feel
      default: '#000000',  // Pure black background
      neutral: '#262626'   // Dark gray for neutral elements
    },
    action: { active: '#737373', ...COMMON.action },
  },
};

export default palette;
