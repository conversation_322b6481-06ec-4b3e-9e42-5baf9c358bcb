import PropTypes from 'prop-types';
import { Stack, Typography, Tooltip } from '@mui/material';
import Iconify from '../Iconify';

ScheduleButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  label: PropTypes.string,
  icon: PropTypes.string,
  color: PropTypes.string,
  tooltip: PropTypes.string,
};

export default function ScheduleButton({
  onClick,
  disabled = false,
  label = 'Schedule',
  icon = 'material-symbols:alarm',
  color = 'warning.main',
  tooltip = 'Open scheduler',
  ...other
}) {
  const handleClick = () => {
    if (disabled) return;
    onClick();
  };

  return (
    <Tooltip title={tooltip} placement="bottom" arrow>
      <Stack 
        alignItems="center" 
        justifyContent="center"
        onClick={handleClick}
        sx={{
          cursor: disabled ? 'default' : 'pointer',
          opacity: disabled ? 0.5 : 1,
          '&:hover': {
            opacity: disabled ? 0.5 : 0.8,
          }
        }}
        {...other}
      >
        <Iconify
          icon={icon}
          width={20}
          height={20}
          sx={{ color }}
        />
        <Typography 
          variant="caption" 
          sx={{ 
            color,
            fontSize: '0.65rem',
            fontWeight: 500
          }}
        >
          {label}
        </Typography>
      </Stack>
    </Tooltip>
  );
}
