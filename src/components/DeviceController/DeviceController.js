import PropTypes from 'prop-types';
import { Box, IconButton } from '@mui/material';
import ButtonWrap from '../assets/scripts/button_wrap';

DeviceController.propTypes = {
  onTopLeft: PropTypes.func,
  onTopRight: PropTypes.func,
  onBottomLeft: PropTypes.func,
  onBottomRight: PropTypes.func,
  onCenter: PropTypes.func,
  // Additional smaller buttons
  onSchedule: PropTypes.func,
  onGpsHistory: PropTypes.func,
  onMirrorControl: PropTypes.func,
  topLeftContent: PropTypes.node,
  topRightContent: PropTypes.node,
  bottomLeftContent: PropTypes.node,
  bottomRightContent: PropTypes.node,
  centerContent: PropTypes.node,
  // Additional smaller button content
  scheduleContent: PropTypes.node,
  gpsHistoryContent: PropTypes.node,
  mirrorControlContent: PropTypes.node,
}

const buttonStyle = {
  position: 'absolute',
  transform: 'translate(-50%,-50%)',
  maxWidth: '31%',
  maxHeight: '31%',
  minWidth: '31%',
  minHeight: '31%',
  color: 'grey.100'
};

// Smaller button style for additional controls
const smallButtonStyle = {
  position: 'absolute',
  transform: 'translate(-50%,-50%)',
  maxWidth: '22%',
  maxHeight: '22%',
  minWidth: '22%',
  minHeight: '22%',
  color: 'grey.100',
  fontSize: '0.8rem'
};

export default function DeviceController({
  onTopLeft,
  onTopRight,
  onBottomLeft,
  onBottomRight,
  onCenter,
  // Additional smaller button handlers
  onSchedule,
  onGpsHistory,
  onMirrorControl,
  topLeftContent,
  topRightContent,
  bottomLeftContent,
  bottomRightContent,
  centerContent,
  // Additional smaller button content
  scheduleContent,
  gpsHistoryContent,
  mirrorControlContent,
  ...other }) {

  return (
    <Box
      position={'relative'}
      width={240}
      height={280} // Increased height to accommodate additional buttons
      m={'auto'}
      sx={{
        overflow: 'visible',
        paddingTop: '10px',
        marginTop: '-10px'
      }}
      {...other}
    >
      
      <ButtonWrap color={'grey.50032'} />

      {/* Main control buttons */}
      <IconButton sx={{ top: '18%', left: '20.8%', ...buttonStyle }} onClick={onTopLeft} >
        {topLeftContent}
      </IconButton>
      <IconButton sx={{ top: '18%', left: '78.8%', ...buttonStyle }} onClick={onTopRight}>
        {topRightContent}
      </IconButton>
      <IconButton sx={{ top: '68%', left: '20.8%', ...buttonStyle }} onClick={onBottomLeft}>
        {bottomLeftContent}
      </IconButton>
      <IconButton sx={{ top: '68%', left: '78.8%', ...buttonStyle }} onClick={onBottomRight}>
        {bottomRightContent}
      </IconButton>
      <IconButton sx={{ top: '43%', left: '49.5%', ...buttonStyle }} onClick={onCenter}>
        {centerContent}
      </IconButton>

      {/* Additional smaller control buttons */}
      {/* GPS History button - below GPS button (top-left) */}
      {gpsHistoryContent && (
        <IconButton sx={{ top: '35%', left: '20.8%', ...smallButtonStyle }} onClick={onGpsHistory}>
          {gpsHistoryContent}
        </IconButton>
      )}

      {/* Mirror Control button - below Lock button (bottom-left) */}
      {mirrorControlContent && (
        <IconButton sx={{ top: '85%', left: '20.8%', ...smallButtonStyle }} onClick={onMirrorControl}>
          {mirrorControlContent}
        </IconButton>
      )}

      {/* Schedule button - below Power button (bottom-right) */}
      {scheduleContent && (
        <IconButton sx={{ top: '85%', left: '78.8%', ...smallButtonStyle }} onClick={onSchedule}>
          {scheduleContent}
        </IconButton>
      )}
    </Box>
  )
}
