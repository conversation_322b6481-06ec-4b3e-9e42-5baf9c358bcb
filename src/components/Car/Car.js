import { Box, Typography } from "@mui/material";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

export default function Car({ device, status, action, color = 'black', title = "" }) {
    const { t } = useTranslation();


    return (
        <Box sx = {{width:388,}}>
            <Box sx={{ width: 388, height: 420, position: 'relative' }}>
                <img src={`/images/car-${color}-body.png`} style={{ width: "100%", position: "absolute", left: 0, top: 30 }}></img>
                {(status && status.sta === 1) && <>
                    <img src={'/images/car-front-light.svg'} style={{ width: "100%", position: "absolute", left: 0, top: 30 }}></img>
                </>
                }
                {status && (status?.sta === 1 || status?.light === 1) && <>
                    <img src={'/images/car-rear-light.svg'} style={{ width: "100%", position: "absolute", left: 0, top: 30 }}></img>
                </>
                }
                <img src={`/images/car-${color}-l-door.svg`} style={{ position: "absolute", width: "100%", left: 0, top: 30 }}></img>
                <img src={`/images/car-${color}-r-door.svg`} style={{ position: "absolute", width: "100%", left: 0, top: 30 }}></img>
            </Box>
            {/* Removed Typography components for device name and speed */}
        </Box>

    )
}
