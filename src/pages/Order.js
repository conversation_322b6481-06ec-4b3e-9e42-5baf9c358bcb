
import { useSnackbar } from 'notistack';


// @mui 
import { <PERSON><PERSON>,  Grid,  Container, Typo<PERSON>, Divider, TextField, Button, Chip, Select, MenuItem, FormControl, InputLabel } from '@mui/material';

import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import dayjs from 'dayjs';
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
// hooks
import useAuth from '../hooks/useAuth';
// components
import Page from '../components/Page';
import axios from '../utils/axios';
import Layout from '../layout';
import { PaymentDialog } from './PaymentDialog';
import { Icon } from '@iconify/react';

// ----------------------------------------------------------------------

export default function DeviceProfile() {
  const { initialize, user } = useAuth();
  const [paymentRequest, setPaymentRequest] = useState(false);
  const { t } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();
  const [bankList, setBankList] = useState([]);
  const [qrImage, setQrImage] = useState();
  const [userPhoneNumber] = useState(user?.phoneNumber);
  const [carModel, setCarModel] = useState("");
  const [address, setAddress] = useState("");
  const [availableTime, setAvailableTime] = useState(dayjs(new Date()));
  const [isSpareKey, setIsSpareKey] = useState(true);
  const [currentOrder, setCurrentOrder] = useState();


  const onSubmitOrder = async () => {


    const data = {
      phoneNumber: userPhoneNumber,
      CarModel: carModel,
      AvialableTime: availableTime.toString(),
      address: address,
      isSpareKey: isSpareKey,

      page: 'order',
    }

    const res = await axios.post(`/api/device/order-confirm`, { ...data });
    console.log("is order", res.data)
    try {
      const qpay = res?.data?.qpay;
      if (res?.data?.success) {
        if (qpay && qpay.success) {
          enqueueSnackbar(`${res?.data?.message}, but not paid yet`, { variant: 'success' });
          setTimeout(() => {
            setQrImage(qpay.bankList?.qr_image);
            setBankList(qpay.bankList?.urls);
            setPaymentRequest(true);
          }, 1000);
        }
        else {
          enqueueSnackbar(qpay?.message, { variant: 'error' });
        }
        // enqueueSnackbar(res?.data?.message, { variant: 'success' });

      }
      else {
        if (res?.data?.order) {
          if (!res.data.order?.paid) {

            if (qpay && qpay.success) {
              enqueueSnackbar(`${res?.data?.message}, but not paid yet`, { variant: 'error' });
              setTimeout(() => {
                setQrImage(qpay.bankList?.qr_image);

                setBankList(qpay.bankList?.urls);
                setPaymentRequest(true);
              }, 1000);

            }

            else {
              enqueueSnackbar(qpay?.message, { variant: 'error' });
            }
          }

          // order is already and already paid.
          else {
            enqueueSnackbar(res?.data?.message, { variant: 'error' });
          }
        }

      }
    }
    catch (err) {
      // console.log(err);
    }





  }


  useEffect(() => {
    // Define the async function inside the effect
    async function fetchData() {
      try {
        const res = await axios.post(`/api/device/order-info`);
        if (res && res.status === 200 && res.data.success) {
          setCurrentOrder(res.data.order);
          setCarModel(res.data.order.CarModel);
          setAddress(res.data.order.address);
          setAvailableTime(res.data.order.AvialableTime);
          setIsSpareKey(res.data.order.isSpareKey);
        }
      } catch (err) {
        console.error("Error fetching order info:", err);
      }
    }
  
    fetchData();
  }, []);  // Empty dependency array means it only runs on mount
  console.log('is visible', paymentRequest);
  return (
    <Page title="Order Profile">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth={'md'}>
        <Grid container spacing={3}  >

          <Grid item xs={12} >
            <Typography variant='h4' sx={{ mt: 2 }}>
              {t("order.order_detail")}
              <Chip sx={{ ml: 2 }} label={user?.status}
                size="small" />
            </Typography>
            <Divider sx={{ mb: 4, mt: 1 }} />
            {currentOrder &&
              <Stack spacing={2} direction='row' sx = {{mb:4, justifyContent:'center', alignItems:'center'}} >
                {currentOrder?.paid?<Icon width={24}  icon = "flat-color-icons:paid"></Icon>:<Icon width = {24} icon = 'mdi:question-mark-circle-outline'></Icon>}
                <Typography variant='subtitle2'>{currentOrder.paid?currentOrder.invoiceId:'Not Paid yet'}, </Typography>
                <Typography variant='subtitle2'>Install Status:</Typography>
                {currentOrder?.isInstalled?<Icon icon="entypo:install" color ='green'></Icon> : <Icon icon='entypo:uninstall'></Icon>}
              </Stack>
            }
            <Stack spacing={3}>

              <TextField label={`${t("order.car_model")}`} onChange={(e) => { setCarModel(e.target.value) }} value={carModel} />
              <TextField label={`${t("order.address")}`} onChange={(e) => { setAddress(e.target.value) }} value={address} />
              <LocalizationProvider dateAdapter={AdapterDayjs} >
                <DateTimePicker
                  label={`${t("order.date_time")}`}
                  value={availableTime}
                  onChange={(e) => { console.log(e); setAvailableTime(e) }}
                  renderInput={(params) => <TextField {...params} sx={{ flexGrow: 1 }} />}
                />
              </LocalizationProvider>
              <FormControl>
                <InputLabel id="period-select-label">{t("order.spare_key")}</InputLabel>
                <Select label="Period" onChange={(e) => { setIsSpareKey(e.target.value) }} value={isSpareKey} labelId="period-select-label">
                  <MenuItem value={true}>{`${t("order.yes")}`}</MenuItem>
                  <MenuItem value={false}>{`${t("order.no")}`}</MenuItem>
                </Select>
              </FormControl>
              {/* <Typography sx={{ textAlign: 'right' }}>{t("order.order_price")}: {fShortenNumber(orderPrice)}</Typography> */}

              <Button fullWidth size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                onClick={() => { onSubmitOrder() }} variant="contained"  >
                {t("order.submit_order")}
              </Button>
              <Divider sx={{ mb: 4, mt: 1 }} />
              <Typography variant='body2' color="green" sx={{ mt: 2 }}>
                {t("order.order_pricing")}
              </Typography>
              <Divider sx={{ mb: 4, mt: 1 }} />
            </Stack>


          </Grid>
        </Grid>
      </Container>
      {paymentRequest && <PaymentDialog qrImage={qrImage} open={paymentRequest} onClose={() => { initialize(); setPaymentRequest(false); }} bankList={bankList} />}
    </Page>
  );
}
